import OpenAI from "openai";
import {
  MarketData,
  AIAnalysisResult,
  TradingSignal,
  TechnicalIndicators,
  RiskManagement,
  TrendAnalysis,
  RollingStrategy,
  OpenAIConfig,
} from "@/types/trading";
import { configService } from "./config-service";
import { serverPredictionTracker } from "./server-prediction-tracker";
import { serverDataQualityChecker } from "./data-quality-checker";
import { FeishuWebhookService } from "./feishu-webhook";

export class AIAnalysisService {
  private openai: OpenAI | null = null;
  private static instance: AIAnalysisService;
  private currentConfig: OpenAIConfig | null = null;

  constructor() {
    this.initializeOpenAI();
  }

  private initializeOpenAI() {
    // 只在服务端初始化 OpenAI 客户端
    if (typeof window !== "undefined") {
      // 在客户端，不初始化 OpenAI 客户端
      return;
    }

    const config = configService.getConfig();

    if (config.apiKey) {
      this.openai = new OpenAI({
        apiKey: config.apiKey,
        baseURL: config.baseURL,
      });
      this.currentConfig = config;
    }
  }

  static getInstance(): AIAnalysisService {
    if (!AIAnalysisService.instance) {
      AIAnalysisService.instance = new AIAnalysisService();
    }
    return AIAnalysisService.instance;
  }

  /**
   * 重新初始化 OpenAI 客户端（当配置更新时调用）
   */
  reinitialize(): void {
    this.initializeOpenAI();
  }

  /**
   * 检查是否已配置 API
   */
  isConfigured(): boolean {
    // 在客户端，直接检查配置服务中的配置
    if (typeof window !== "undefined") {
      const config = configService.getConfig();
      return !!(config.apiKey && config.apiKey.trim());
    }

    // 在服务端，检查 OpenAI 客户端是否已初始化
    return this.openai !== null && this.currentConfig !== null;
  }

  /**
   * 获取当前配置
   */
  getCurrentConfig(): OpenAIConfig | null {
    return this.currentConfig;
  }

  /**
   * 生成AI分析系统提示词
   */
  private generateSystemPrompt(
    riskTolerance: "LOW" | "MEDIUM" | "HIGH" = "MEDIUM"
  ): string {
    // 获取历史准确率统计（服务端异步调用，这里使用默认值）
    const accuracyStats = {
      totalPredictions: 0,
      verifiedPredictions: 0,
      directionAccuracy: 0,
      priceAccuracy: 0,
      overallAccuracy: 0,
      recentTrend: 0,
    };
    const improvements: string[] = [];
    // 根据风险偏好生成个性化的风险管理策略
    const riskProfile = {
      LOW: {
        label: "保守型",
        maxPosition: "1-3%",
        stopLoss: "1.5-2%",
        leverage: "1-2倍",
        strategy: "严格止损控制，优先选择低波动币种，保守的加仓策略",
        characteristics:
          "单次仓位不超过3%，严格止损控制，优先选择低波动币种，保守的加仓策略",
      },
      MEDIUM: {
        label: "平衡型",
        maxPosition: "2-5%",
        stopLoss: "2-2.5%",
        leverage: "1-3倍",
        strategy:
          "平衡风险与收益，灵活的风险控制，平衡的加仓策略，适度的杠杆使用",
        characteristics:
          "单次仓位2-5%，灵活的风险控制，平衡的加仓策略，适度的杠杆使用",
      },
      HIGH: {
        label: "激进型",
        maxPosition: "3-7%",
        stopLoss: "2.5-3%",
        leverage: "2-5倍",
        strategy: "追求高收益，积极的加仓策略，较高的杠杆使用，快速止盈止损",
        characteristics:
          "单次仓位可达7%，积极的加仓策略，较高的杠杆使用，快速止盈止损",
      },
    }[riskTolerance];

    // 构建历史表现反馈
    let performanceFeedback = "";
    if (accuracyStats.verifiedPredictions > 10) {
      performanceFeedback = `
## 历史预测表现分析：
- 总预测次数：${accuracyStats.totalPredictions}，已验证：${
        accuracyStats.verifiedPredictions
      }
- 方向预测准确率：${accuracyStats.directionAccuracy.toFixed(1)}%
- 价格预测准确率：${accuracyStats.priceAccuracy.toFixed(1)}%
- 综合准确率：${accuracyStats.overallAccuracy.toFixed(1)}%
- 最近趋势：${accuracyStats.recentTrend > 0 ? "改善" : "下降"}

## 改进建议：
${improvements.map((suggestion) => `- ${suggestion}`).join("\n")}

**请根据以上历史表现调整分析策略，特别关注准确率较低的方面。**
`;
    }

    return `你是一位专业的加密货币交易分析师，专注于提供精准的交易建议。
${performanceFeedback}
## 当前用户风险偏好：${riskProfile.label}
- 最大仓位：${riskProfile.maxPosition}
- 止损范围：${riskProfile.stopLoss}
- 杠杆倍数：${riskProfile.leverage}
- 策略特点：${riskProfile.strategy}

## 核心原则：
- 基于数据驱动的客观分析
- 严格的风险管理和资金保护（根据${riskProfile.label}风险偏好调整）
- 识别高概率交易机会
- 理解市场心理和主力行为

## 分析重点（按重要性排序）：
1. **趋势方向确认** - 基于多时间框架确定主导趋势，避免逆势交易
2. **关键位精确识别** - 基于成交量和价格行为确定有效支撑阻力位
3. **成交量与价格背离分析** - 识别真实突破vs假突破
4. **技术指标组合验证** - 多指标共振确认信号强度
5. **市场微观结构分析** - 订单流、资金流向、持仓变化
6. **风险收益比优化** - 确保每笔交易风险收益比 > 3:1，提高达标概率

## 主力行为识别：
- 对敲交易：识别虚假成交量
- 价格托盘：关键位置的人工支撑
- 假突破：故意制造技术信号误导
- 做局模式：吸筹、拉升、出货、砸盘阶段识别



## 交易铁律（达标率优化版）：
✅ 趋势确认后才加仓（突破回踩不破 + 成交量确认）
✅ 动态仓位管理：基础仓位5%，高信心度时可达8%
✅ 智能加仓：每突破关键位加2-4%（根据信心度调整）
✅ 追踪止损：止损随盈利上移，锁死利润，永不倒亏
✅ 优化止盈策略：第一目标2-3%止盈40%，第二目标4-6%止盈40%，第三目标8-12%止盈20%
✅ 严格止损控制：亏损1.5-2%立即止损，绝不扩大损失
✅ 盈利保护：赚到的钱绝不让市场抢回去
✅ 阶梯式减仓：行情尾声逐步落袋为安
✅ 识别庄家意图：顺势而为，避免与庄家对抗
✅ 跟随聪明钱：关注大户动向和资金流向
✅ 高频机会：识别1-5分钟级别的剥头皮机会
✅ 情绪反转：在极度恐慌时买入，极度贪婪时卖出

## 风险控制（根据${riskProfile.label}风险偏好调整）：
- 最大单次仓位：${riskProfile.maxPosition}
- 止损幅度：${riskProfile.stopLoss}
- 杠杆倍数：${riskProfile.leverage}
- 严格执行止损，绝不抱有侥幸心理
- 市场不会奖励贪心，但一定会惩罚贪婪
- 识别庄家陷阱，避免在关键位置被套
- 期货合约交易的风险管理（默认使用期货合约）
- 风险管理策略：${riskProfile.characteristics}

## 分析要求（必须全部完成）：
1. **多时间维度综合分析** - 结合月线、日线、小时线、30分钟线、1分钟线数据
2. **趋势识别** - 明确判断极短期(15分钟-2小时)、短期(2-8小时)、中期(8-24小时)趋势方向和强度
3. **关键位识别** - 基于技术指标和价格行为确定精确的支撑阻力位
4. **市场结构分析** - 评估当前是突破、回调、震荡还是反转阶段
5. **成交量分析** - 结合成交量变化确认价格走势的有效性
6. **庄家行为深度分析** - 识别庄家操盘手法、心理状态和意图
7. **持仓分布分析** - 分析大户、散户、机构的持仓变化和行为模式
8. **期货合约分析** - 基于期货合约特性提供专业建议（默认期货交易）
9. **具体交易建议** - 提供明确的入场价格、止损位、止盈位和仓位大小
10. **多时间段预测分析** - 必须提供极短期(15分钟-2小时)、短期(2-8小时)、中期(8-24小时)三个时间段的详细预测


## 决策逻辑（严格遵循，优化版）：
### BUY信号条件（必须满足至少3个条件）：
1. **趋势确认**：
   - 极短期(15分钟-2小时)趋势向上 OR 短期(2-8小时)趋势向上
   - 价格在EMA20上方且EMA20向上倾斜
   - 近期K线实体多为阳线，显示买盘力量

2. **技术指标支撑**：
   - RSI > 35且呈上升趋势（避免超卖反弹陷阱）
   - MACD线在信号线上方或即将金叉，且MACD柱状图转正
   - 价格突破关键阻力位或在强支撑位获得支撑

3. **成交量确认**：
   - 突破时成交量明显放大（至少1.5倍平均成交量）
   - 回调时成交量萎缩，显示抛压有限

4. **风险控制**：
   - 止损位明确且风险收益比 > 3:1（提高达标率要求）
   - 当前价格距离强支撑位不超过2%（更严格的风险控制）

### SELL信号条件（必须满足至少3个条件）：
1. **趋势确认**：
   - 极短期(15分钟-2小时)趋势向下 OR 短期(2-8小时)趋势向下
   - 价格在EMA20下方且EMA20向下倾斜
   - 近期K线实体多为阴线，显示卖盘力量

2. **技术指标支撑**：
   - RSI < 65且呈下降趋势（避免超买回调陷阱）
   - MACD线在信号线下方或即将死叉，且MACD柱状图转负
   - 价格跌破关键支撑位或在强阻力位遇阻

3. **成交量确认**：
   - 跌破时成交量明显放大（至少1.5倍平均成交量）
   - 反弹时成交量萎缩，显示买盘不足

4. **风险控制**：
   - 止损位明确且风险收益比 > 3:1（提高达标率要求）
   - 当前价格距离强阻力位不超过2%（更严格的风险控制）

### HOLD信号条件（谨慎使用）：
- 多个时间框架趋势方向冲突且无明显主导趋势
- 技术指标处于中性区域且无明确方向
- 成交量持续萎缩，市场缺乏参与度
- 关键技术位附近震荡，方向选择尚未明确
- 风险收益比 < 3:1 且无明确催化剂（提高交易门槛）

## 数值计算要求：
1. **entryPrice**: 必须基于当前价格和技术分析给出具体入场价格，不能为0
2. **stopLoss**: 必须基于支撑阻力位和风险控制原则计算，不能为0
3. **takeProfit**: 必须提供至少2个止盈位，基于阻力位和风险收益比
4. **riskReward**: 必须计算实际的风险收益比 = (止盈价-入场价)/(入场价-止损价)
5. **winRate**: 基于当前市场条件和信号强度估算胜率(30-90%)
6. **expectedReturn**: 基于风险收益比和胜率计算期望收益率

## 价格逻辑验证（极其重要）：
**BUY信号的价格逻辑**：
- 止盈价格 > 入场价格 > 止损价格
- 例如：止盈$108000 > 入场$100500 > 止损$99000 ✓

**SELL信号的价格逻辑**：
- 止损价格 > 入场价格 > 止盈价格
- 例如：止损$102000 > 入场$100500 > 止盈$98000 ✓

**错误示例（绝对禁止）**：
- BUY信号但止盈价格低于入场价格 ✗
- SELL信号但止盈价格高于入场价格 ✗

## 多时间段预测要求（极其重要）：
必须提供以下三个时间段的详细预测分析：

### 极短期预测（15分钟-2小时内）：
- 基于1分钟、5分钟、15分钟线数据
- 重点关注快速价格波动和即时市场情绪
- 适合剥头皮交易，快进快出
- 风险等级：HIGH，建议仓位：小仓位(1-3%)
- 预测价格波动范围和关键阻力支撑位
- **这是默认的交易建议时间段，因为主要做极短期快速交易**
- **主要的交易信号(signal字段)应该与极短期预测的交易策略保持一致**

### 短期预测（2-8小时内）：
- 基于15分钟、30分钟、1小时线数据
- 关注日内趋势延续性和关键技术位突破
- 适合日内波段交易
- 风险等级：MEDIUM，建议仓位：中等仓位(3-5%)
- 分析主力资金流向和操盘意图

### 中期预测（8-24小时内）：
- 基于1小时、4小时、日线数据
- 重点分析主要趋势方向和重要支撑阻力位
- 适合短线持仓交易
- 风险等级：MEDIUM-LOW，建议仓位：主要仓位(5-8%)
- 提供参考性的中长期趋势分析

每个时间段必须包含：
1. 趋势方向和信心度
2. 价格目标区间（最高、最低、最可能价格）
3. 具体交易策略（入场、止损、止盈）
4. 关键事件和风险因素
5. 市场条件评估

## 输出格式要求：
- 必须返回纯JSON格式，不要包含任何markdown代码块标记
- 不要使用代码块标记包围响应
- 直接返回有效的JSON对象
- 所有数值字段不能为0或空数组（除非确实是HOLD信号）
- 必须包含完整的multiTimeframePrediction字段

请始终以数据为准，保持客观理性，但必须提供具体可执行的建议。避免过度保守，在风险可控的前提下积极寻找交易机会。`;
  }

  /**
   * 生成用户分析请求提示词
   */
  private generateAnalysisPrompt(
    marketData: MarketData,
    indicators: TechnicalIndicators,
    riskTolerance: "LOW" | "MEDIUM" | "HIGH" = "MEDIUM",
    qualityReport?: any
  ): string {
    const { symbol, monthly, daily, hourly, thirtyMin, oneMin } = marketData;

    // 根据风险偏好生成仓位建议（优化版 - 降低回撤）
    const positionSizes = {
      LOW: { ultraShort: "1-2%", short: "1.5-2.5%", medium: "2-3%" },
      MEDIUM: { ultraShort: "2-3%", short: "2.5-4%", medium: "3-5%" },
      HIGH: { ultraShort: "3-4%", short: "4-6%", medium: "5-7%" },
    }[riskTolerance];

    // 获取最新价格和变化
    const latestPrice = oneMin[oneMin.length - 1]?.close || 0;
    const dailyChange = daily[daily.length - 1]?.changePercent || 0;
    const hourlyChange = hourly[hourly.length - 1]?.changePercent || 0;

    // 计算波动率和成交量分析
    const volatility = this.calculateVolatility(daily);
    const volume24h = daily[daily.length - 1]?.volume || 0;
    const avgVolume = daily.slice(-7).reduce((sum, d) => sum + d.volume, 0) / 7;
    const volumeRatio = volume24h / avgVolume;

    // 计算价格相对位置
    const priceVsBollingerUpper =
      ((latestPrice - indicators.bollinger.upper) /
        indicators.bollinger.upper) *
      100;
    const priceVsBollingerLower =
      ((latestPrice - indicators.bollinger.lower) /
        indicators.bollinger.lower) *
      100;
    const priceVsEMA20 =
      ((latestPrice - indicators.ema.ema20) / indicators.ema.ema20) * 100;
    const priceVsEMA50 =
      ((latestPrice - indicators.ema.ema50) / indicators.ema.ema50) * 100;

    // MACD信号分析
    const macdSignal =
      indicators.macd.macd > indicators.macd.signal ? "金叉" : "死叉";
    const macdStrength = Math.abs(
      indicators.macd.macd - indicators.macd.signal
    );

    // 构建数据质量信息
    let qualityInfo = "";
    if (qualityReport) {
      qualityInfo = `
## 数据质量评估：
- 质量评分：${qualityReport.score.toFixed(1)}/100
- 数据状态：${qualityReport.isValid ? "良好" : "存在问题"}
${
  qualityReport.issues.length > 0
    ? `- 质量问题：${qualityReport.issues.join(", ")}`
    : ""
}
${
  qualityReport.warnings.length > 0
    ? `- 注意事项：${qualityReport.warnings.join(", ")}`
    : ""
}
${
  qualityReport.recommendations.length > 0
    ? `- 建议：${qualityReport.recommendations.join(", ")}`
    : ""
}

**请根据数据质量情况调整分析的信心度和建议的保守程度。**
`;
    }

    return `请分析 ${symbol} 的交易机会，必须提供具体可执行的交易建议：
${qualityInfo}
## 用户风险偏好设置：
- 风险类型：${
      riskTolerance === "LOW"
        ? "保守型"
        : riskTolerance === "MEDIUM"
        ? "平衡型"
        : "激进型"
    }
- 极短期仓位建议：${positionSizes.ultraShort}
- 短期仓位建议：${positionSizes.short}
- 中期仓位建议：${positionSizes.medium}
- 请根据用户的风险偏好调整交易建议和仓位大小

## 市场数据概览：
- 当前价格: $${latestPrice.toFixed(4)}
- 24小时变化: ${dailyChange.toFixed(2)}%
- 1小时变化: ${hourlyChange.toFixed(2)}%
- 24小时成交量: ${volume24h.toFixed(0)}
- 7日平均成交量: ${avgVolume.toFixed(0)}
- 成交量比率: ${volumeRatio.toFixed(2)}x (${
      volumeRatio > 1.2 ? "放量" : volumeRatio < 0.8 ? "缩量" : "正常"
    })
- 波动率: ${volatility.toFixed(2)}%

## 技术指标详细分析：
- RSI(14): ${indicators.rsi.toFixed(2)} (${
      indicators.rsi > 70 ? "超买" : indicators.rsi < 30 ? "超卖" : "中性"
    })
- MACD: ${indicators.macd.macd.toFixed(
      4
    )} (信号线: ${indicators.macd.signal.toFixed(
      4
    )}, ${macdSignal}, 强度: ${macdStrength.toFixed(4)})
- 布林带位置: 上轨 ${indicators.bollinger.upper.toFixed(
      4
    )}, 中轨 ${indicators.bollinger.middle.toFixed(
      4
    )}, 下轨 ${indicators.bollinger.lower.toFixed(4)}
- 价格相对布林带: 距上轨${priceVsBollingerUpper.toFixed(
      2
    )}%, 距下轨${priceVsBollingerLower.toFixed(2)}%
- EMA均线: EMA20=${indicators.ema.ema20.toFixed(
      4
    )}, EMA50=${indicators.ema.ema50.toFixed(
      4
    )}, EMA200=${indicators.ema.ema200.toFixed(4)}
- 价格相对均线: 距EMA20${priceVsEMA20.toFixed(
      2
    )}%, 距EMA50${priceVsEMA50.toFixed(2)}%
- 关键位: 支撑${indicators.support.toFixed(
      4
    )}, 阻力${indicators.resistance.toFixed(4)}

## 多时间维度趋势分析：
### 月线趋势 (36个月):
最近3个月变化: ${monthly
      .slice(-3)
      .map((m) => `${m.changePercent.toFixed(1)}%`)
      .join(", ")}
月线趋势: ${
      monthly.slice(-3).every((m) => m.changePercent > 0)
        ? "强势上涨"
        : monthly.slice(-3).every((m) => m.changePercent < 0)
        ? "持续下跌"
        : "震荡整理"
    }

### 日线趋势 (30天):
最近7天变化: ${daily
      .slice(-7)
      .map((d) => `${d.changePercent.toFixed(1)}%`)
      .join(", ")}
日线趋势: ${
      daily.slice(-7).filter((d) => d.changePercent > 0).length >= 5
        ? "多头主导"
        : daily.slice(-7).filter((d) => d.changePercent < 0).length >= 5
        ? "空头主导"
        : "震荡"
    }

### 小时线趋势 (最近24小时):
变化: ${hourly
      .slice(-24)
      .map((h) => `${h.changePercent.toFixed(1)}%`)
      .join(", ")}
小时线动量: ${hourly
      .slice(-6)
      .reduce((sum, h) => sum + h.changePercent, 0)
      .toFixed(2)}% (最近6小时累计)

### 30分钟线 (最近6小时):
变化: ${thirtyMin
      .slice(-12)
      .map((t) => `${t.changePercent.toFixed(1)}%`)
      .join(", ")}

### 1分钟线 (最近30分钟):
变化: ${oneMin
      .slice(-30)
      .map((o) => `${o.changePercent.toFixed(1)}%`)
      .join(", ")}

## 技术指标权重配置（严格遵循）：
### 趋势确认指标（权重40%）：
1. **EMA均线系统**（权重15%）：
   - EMA20方向和角度（主要趋势）
   - 价格与EMA20/50的位置关系
   - 均线多头/空头排列确认

2. **MACD系统**（权重15%）：
   - MACD线与信号线关系（金叉/死叉）
   - MACD柱状图变化趋势
   - MACD背离信号识别

3. **价格行为**（权重10%）：
   - K线实体大小和影线长度
   - 连续阳线/阴线模式
   - 关键位置的价格反应

### 动量指标（权重25%）：
1. **RSI相对强弱**（权重15%）：
   - RSI数值和趋势方向
   - 超买超卖区域判断
   - RSI背离信号

2. **成交量分析**（权重10%）：
   - 成交量与价格配合度
   - 放量突破/缩量回调确认
   - 异常成交量识别

### 支撑阻力位（权重20%）：
1. **关键价格位**（权重15%）：
   - 历史高低点位置
   - 成交密集区域
   - 整数关口心理位

2. **布林带位置**（权重5%）：
   - 价格在布林带中的位置
   - 布林带收缩/扩张状态

### 市场结构（权重15%）：
1. **多时间框架一致性**（权重10%）：
   - 各时间框架趋势方向统一性
   - 关键位在不同周期的有效性

2. **风险收益比**（权重5%）：
   - 入场点到止损距离
   - 止盈目标的合理性

## 核心分析要求：
请基于以上权重配置和数据进行分析：
1. **趋势方向确认**（最高优先级）：
   - 基于EMA系统确定主导趋势
   - MACD信号验证趋势强度
   - 多时间框架趋势一致性检查

2. **入场时机判断**：
   - RSI是否处于合理区间
   - 成交量是否支撑价格走势
   - 关键支撑阻力位的有效性

3. **风险管理优化**：
   - 基于ATR计算合理止损距离
   - 确保风险收益比 > 3:1（提高盈利概率）
   - 考虑市场波动率调整仓位
   - 优先选择高胜率、高达标率的交易机会

## 市场环境识别与适应策略：
### 趋势市场（单边行情）：
**识别特征**：
- 连续3个以上同方向K线
- 成交量持续放大
- 技术指标同向共振
- 突破关键位后无回调

**交易策略**：
- 积极追涨杀跌，顺势而为
- 适当提高仓位（风险偏好内）
- 采用追踪止损，让利润奔跑
- 关注趋势延续信号
- 止盈目标：第一目标2.5%，第二目标5%，第三目标10%

### 震荡市场（区间整理）：
**识别特征**：
- 价格在明确区间内波动
- 成交量相对萎缩
- 技术指标在中性区域摆动
- 支撑阻力位反复测试

**交易策略**：
- 高抛低吸，区间操作
- 降低仓位，控制风险
- 快进快出，避免过夜
- 重点关注区间边界突破
- 止盈目标：第一目标1.5%，第二目标3%，快速获利了结

### 反转市场（趋势转换）：
**识别特征**：
- 出现明显背离信号
- 关键位被有效突破
- 成交量异常放大
- 技术指标发生转向

**交易策略**：
- 谨慎观察，等待确认
- 小仓位试探性建仓
- 严格止损，快速止盈
- 关注反转确认信号
- 止盈目标：第一目标2%，第二目标4%，保守获利

### 高波动市场：
**识别特征**：
- 日内波动超过3%
- 成交量大幅放大
- 技术指标剧烈摆动
- 消息面影响明显

**交易策略**：
- 降低杠杆，控制风险
- 缩短持仓时间
- 扩大止损空间
- 重点关注风险管理
- 止盈目标：第一目标3%，第二目标6%，适应高波动

### 低波动熊市（当前主要环境）：
**识别特征**：
- 日内波动小于2%
- 成交量持续萎缩
- 价格缓慢下跌或横盘
- 买盘力量不足
- 技术指标偏空但不极端

**交易策略**：
- 极度谨慎，优先观望
- 仓位控制在1-3%以内
- 快进快出，避免过夜
- 重点关注反弹机会
- 止盈目标：第一目标1.5%，第二目标3%，快速获利了结
- 止损设置：严格控制在1%以内
- 优先做空反弹，谨慎做多
- 关注成交量放大的突破信号

## 分析要求：
基于以上数据和市场环境识别，你必须：

1. **识别当前市场环境** - 判断属于哪种市场类型
2. **选择对应交易策略** - 根据市场环境调整策略
3. **明确判断趋势方向** - 基于数据给出倾向性判断
4. **提供具体交易信号** - 给出BUY/SELL/HOLD信号，包含具体价格
5. **计算风险管理参数** - 基于技术分析和市场环境计算止损止盈
6. **评估胜率和收益** - 基于当前市场条件和环境估算
7. **识别关键支撑阻力位** - 提供关键价格位
8. **调整仓位建议** - 根据市场环境和风险偏好调整仓位

## 决策参考：
- 当前价格 vs 支撑位距离: ${(
      ((latestPrice - indicators.support) / indicators.support) *
      100
    ).toFixed(2)}%
- 当前价格 vs 阻力位距离: ${(
      ((indicators.resistance - latestPrice) / latestPrice) *
      100
    ).toFixed(2)}%
- RSI是否超买超卖: ${
      indicators.rsi > 70
        ? "超买，考虑卖出"
        : indicators.rsi < 30
        ? "超卖，考虑买入"
        : "RSI中性区域"
    }
- MACD信号: ${macdSignal}，强度${macdStrength > 10 ? "强" : "弱"}
- 成交量确认: ${volumeRatio > 1.2 ? "有成交量支撑" : "成交量不足，需谨慎"}

请以纯JSON格式返回分析结果，不要使用markdown代码块标记，直接返回有效的JSON对象，包含以下结构：
{
  "trends": {"shortTerm": "BULLISH/BEARISH/NEUTRAL", "mediumTerm": "BULLISH/BEARISH/NEUTRAL", "longTerm": "BULLISH/BEARISH/NEUTRAL"},
  // 注意：trends字段将被multiTimeframePrediction覆盖，确保两者保持一致
  // shortTerm对应极短期(15分钟-2小时)，mediumTerm对应短期(2-8小时)，longTerm对应中期(8-24小时)
  "signal": {"direction": "BUY/SELL/HOLD", "confidence": 0-100, "entryPrice": number, "stopLoss": number, "takeProfit": [number, number], "leverage": 1-3, "reasoning": "详细说明"},
  "risk": {"maxDrawdown": 2-5, "riskReward": 1.5-5.0, "winRate": 30-90, "expectedReturn": -10到50},
  "condition": {"momentum": "STRONG/WEAK/NEUTRAL"},
  "levels": {"support": [number, number], "resistance": [number, number]},
  "marketMaker": {
    "sentiment": "EXTREME_FEAR/FEAR/NEUTRAL/GREED/EXTREME_GREED",
    "pattern": "PUMP_DUMP/ACCUMULATION/DISTRIBUTION/SQUEEZE/NONE",
    "confidence": 0-100,
    "targetPrice": number,
    "reasoning": "主力行为分析"
  },
  "multiTimeframePrediction": {
    "symbol": "${symbol}",
    "timestamp": ${Date.now()},
    "currentPrice": number,
    "predictions": {
      "ultraShort": {
        "timeframe": "ULTRA_SHORT",
        "duration": "15分钟-2小时",
        "startTime": ${Date.now()},
        "endTime": ${Date.now() + 2 * 60 * 60 * 1000},
        "trend": "BULLISH/BEARISH/NEUTRAL",
        "confidence": 0-100,
        "targetPrice": {"high": number, "low": number, "most_likely": number},
        "keyEvents": ["关键事件1", "关键事件2"],
        "riskFactors": ["风险因素1", "风险因素2"],
        "opportunities": ["机会点1", "机会点2"],
        "tradingStrategy": {
          "action": "BUY/SELL/HOLD",
          "entryPrice": number,
          "stopLoss": number,
          "takeProfit": [number, number],
          "positionSize": "${positionSizes.ultraShort}",
          "reasoning": "策略说明",
          "riskLevel": "HIGH",
          "suitableFor": "剥头皮交易，快进快出（主要交易建议）"
        },
        "marketConditions": {
          "volatility": "HIGH/MEDIUM/LOW",
          "volume": "HIGH/MEDIUM/LOW",
          "momentum": "STRONG/WEAK/NEUTRAL"
        }
      },
      "short": {
        "timeframe": "SHORT",
        "duration": "2-8小时",
        "startTime": ${Date.now()},
        "endTime": ${Date.now() + 8 * 60 * 60 * 1000},
        "trend": "BULLISH/BEARISH/NEUTRAL",
        "confidence": 0-100,
        "targetPrice": {"high": number, "low": number, "most_likely": number},
        "keyEvents": ["关键事件1", "关键事件2"],
        "riskFactors": ["风险因素1", "风险因素2"],
        "opportunities": ["机会点1", "机会点2"],
        "tradingStrategy": {
          "action": "BUY/SELL/HOLD",
          "entryPrice": number,
          "stopLoss": number,
          "takeProfit": [number, number],
          "positionSize": "${positionSizes.short}",
          "reasoning": "策略说明",
          "riskLevel": "MEDIUM",
          "suitableFor": "日内波段交易"
        },
        "marketConditions": {
          "volatility": "HIGH/MEDIUM/LOW",
          "volume": "HIGH/MEDIUM/LOW",
          "momentum": "STRONG/WEAK/NEUTRAL"
        }
      },
      "medium": {
        "timeframe": "MEDIUM",
        "duration": "8-24小时",
        "startTime": ${Date.now()},
        "endTime": ${Date.now() + 24 * 60 * 60 * 1000},
        "trend": "BULLISH/BEARISH/NEUTRAL",
        "confidence": 0-100,
        "targetPrice": {"high": number, "low": number, "most_likely": number},
        "keyEvents": ["关键事件1", "关键事件2"],
        "riskFactors": ["风险因素1", "风险因素2"],
        "opportunities": ["机会点1", "机会点2"],
        "tradingStrategy": {
          "action": "BUY/SELL/HOLD",
          "entryPrice": number,
          "stopLoss": number,
          "takeProfit": [number, number],
          "positionSize": "${positionSizes.medium}",
          "reasoning": "策略说明",
          "riskLevel": "MEDIUM-LOW",
          "suitableFor": "短线持仓交易"
        },
        "marketConditions": {
          "volatility": "HIGH/MEDIUM/LOW",
          "volume": "HIGH/MEDIUM/LOW",
          "momentum": "STRONG/WEAK/NEUTRAL"
        }
      }
    },
    "overallAssessment": {
      "dominantTrend": "BULLISH/BEARISH/NEUTRAL",
      "conflictingSignals": true/false,
      "riskLevel": "LOW/MEDIUM/HIGH",
      "recommendation": "综合建议说明",
      "primaryTimeframe": "ultraShort"
    },
    "correlationAnalysis": {
      "timeframeAlignment": 0-100,
      "trendStrength": 0-100,
      "reversal_probability": 0-100
    }
  },
  "insights": "详细的市场洞察和交易逻辑，包含庄家分析",
  "warnings": ["具体的风险提示，包含庄家陷阱"]
}

注意：除非市场完全无方向且风险极高，否则不要返回全部NEUTRAL和0值。要基于数据积极寻找交易机会。`;
  }

  /**
   * 计算价格波动率
   */
  private calculateVolatility(data: any[]): number {
    if (data.length < 2) return 0;

    const returns = data
      .slice(1)
      .map((item, index) => Math.log(item.close / data[index].close));

    const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance =
      returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) /
      returns.length;

    return Math.sqrt(variance) * Math.sqrt(365) * 100; // 年化波动率
  }

  /**
   * 清理AI响应，移除markdown代码块标记
   */
  private cleanAIResponse(response: string): string {
    // 移除markdown代码块标记
    let cleaned = response.trim();

    // 完全移除<think>标签及其内容
    if (cleaned.startsWith("<think>")) {
      cleaned = cleaned.replace(/<think>[\s\S]*?<\/think>/g, "");
    }

    // 移除开头的```json或```
    cleaned = cleaned.replace(/^```(?:json)?\s*\n?/i, "");

    // 移除结尾的```
    cleaned = cleaned.replace(/\n?\s*```\s*$/i, "");

    // 移除其他可能的markdown标记
    cleaned = cleaned.replace(/^```\s*$/gm, "");

    // 移除可能的额外空白字符
    cleaned = cleaned.replace(/^\s+|\s+$/g, "");

    // 如果响应以{开头但不以}结尾，尝试找到最后一个}
    if (cleaned.startsWith("{") && !cleaned.endsWith("}")) {
      const lastBraceIndex = cleaned.lastIndexOf("}");
      if (lastBraceIndex > 0) {
        cleaned = cleaned.substring(0, lastBraceIndex + 1);
      }
    }

    return cleaned;
  }

  /**
   * 执行AI分析（使用传入的配置）
   */
  async analyzeMarketWithConfig(
    marketData: MarketData,
    indicators: TechnicalIndicators,
    config: OpenAIConfig,
    riskTolerance: "LOW" | "MEDIUM" | "HIGH" = "MEDIUM"
  ): Promise<AIAnalysisResult> {
    // 验证配置
    if (!config.apiKey || !config.apiKey.trim()) {
      throw new Error("AI 服务未配置，请前往设置页面配置 OpenAI API");
    }

    // 创建临时的OpenAI客户端
    const openai = new OpenAI({
      apiKey: config.apiKey,
      baseURL: config.baseURL,
      timeout: 300000,
    });

    try {
      // 数据质量检查
      const qualityReport = serverDataQualityChecker.comprehensiveCheck(
        marketData,
        indicators
      );
      console.log("数据质量检查结果:", qualityReport);

      if (!qualityReport.isValid) {
        console.warn("数据质量不佳，可能影响分析准确性");
        console.warn("质量问题:", qualityReport.issues);
        console.warn("警告:", qualityReport.warnings);
      }

      const systemPrompt = this.generateSystemPrompt(riskTolerance);
      const analysisPrompt = this.generateAnalysisPrompt(
        marketData,
        indicators,
        riskTolerance,
        qualityReport
      );
      if (process.env.FEISHU_PROMPT_WEBHOOK_URL) {
        const feishuService = new FeishuWebhookService(
          process.env.FEISHU_PROMPT_WEBHOOK_URL
        );

        feishuService.sendTextMessage(`
        =============== 系统提示 ===============
        ${systemPrompt}
        =============== 分析提示 ===============
        ${analysisPrompt}
        `);
      }

      console.log(config.baseURL, config.model);

      console.log("AI分析请求prompt start");

      // 使用流式读取
      const stream = await openai.chat.completions.create({
        model: config.model,
        messages: [
          { role: "system", content: systemPrompt },
          { role: "user", content: analysisPrompt },
        ],
        stream: true,
        temperature: 0.3,
        // max_tokens: 3000,
        response_format: { type: "json_object" },
      });

      console.log("开始流式读取AI分析结果...");

      let aiResponse = "";
      let chunkCount = 0;

      for await (const chunk of stream) {
        chunkCount++;
        const content = chunk.choices[0]?.delta?.content || "";
        if (content) {
          aiResponse += content;
          // 实时打印接收到的内容
          // console.log(`[${marketData.symbol} Chunk ${chunkCount}] ${content}`);
        }

        // 如果是最后一个chunk，打印完成信息
        if (chunk.choices[0]?.finish_reason) {
          console.log(`流式读取完成，原因: ${chunk.choices[0].finish_reason}`);
        }
      }

      console.log(`总共接收到 ${chunkCount} 个数据块`);
      // console.log("完整AI分析结果:", aiResponse);

      if (!aiResponse) {
        throw new Error("AI分析响应为空");
      }

      // 清理并解析AI响应
      const cleanedResponse = this.cleanAIResponse(aiResponse);
      console.log("清理后的AI响应:", cleanedResponse.substring(0, 30), "...");

      let parsedResponse;
      try {
        parsedResponse = JSON.parse(cleanedResponse);
      } catch (parseError) {
        console.error("JSON解析失败:", parseError);
        console.error("原始响应:", aiResponse);
        console.error("清理后响应:", cleanedResponse);
        throw new Error(
          `AI响应格式错误，无法解析为JSON: ${
            parseError instanceof Error ? parseError.message : "未知错误"
          }`
        );
      }

      // 验证和修正交易信号
      const validatedSignal = this.validateTradingSignal(parsedResponse.signal);
      console.log("交易信号验证完成:", validatedSignal);

      // 确保主要交易信号与极短期预测保持一致（因为主要做极短期交易）
      const ultraShortTermStrategy =
        parsedResponse.multiTimeframePrediction?.predictions?.ultraShort
          ?.tradingStrategy;
      if (ultraShortTermStrategy && validatedSignal) {
        // 如果极短期预测有明确的交易策略，优先使用极短期预测的信号
        if (ultraShortTermStrategy.action !== "HOLD") {
          validatedSignal.direction = ultraShortTermStrategy.action;
          validatedSignal.entryPrice =
            ultraShortTermStrategy.entryPrice || validatedSignal.entryPrice;
          validatedSignal.stopLoss =
            ultraShortTermStrategy.stopLoss || validatedSignal.stopLoss;
          validatedSignal.takeProfit =
            ultraShortTermStrategy.takeProfit || validatedSignal.takeProfit;
          validatedSignal.reasoning = `基于极短期预测(15分钟-2小时)的快速交易策略: ${
            ultraShortTermStrategy.reasoning || ""
          }`;
          console.log("交易信号已与极短期预测对齐:", validatedSignal);
        }
      }

      // 构建标准化的分析结果 - 确保传统趋势分析与多时间段预测保持一致
      const ultraShortTrend =
        parsedResponse.multiTimeframePrediction?.predictions?.ultraShort
          ?.trend || "NEUTRAL";
      const shortTrend =
        parsedResponse.multiTimeframePrediction?.predictions?.short?.trend ||
        "NEUTRAL";
      const mediumTrend =
        parsedResponse.multiTimeframePrediction?.predictions?.medium?.trend ||
        "NEUTRAL";

      const result: AIAnalysisResult = {
        symbol: marketData.symbol,
        timestamp: Date.now(),
        marketTrend: {
          shortTerm: ultraShortTrend, // 极短期(15分钟-2小时)对应短期趋势
          mediumTerm: shortTrend, // 短期(2-8小时)对应中期趋势
          longTerm: mediumTrend, // 中期(8-24小时)对应长期趋势
        },
        technicalIndicators: indicators,
        tradingSignal: {
          direction:
            this.convertSignalDirection(validatedSignal?.direction) || "HOLD",
          confidence: validatedSignal?.confidence || 50,
          entryPrice:
            validatedSignal?.entryPrice ||
            marketData.oneMin[marketData.oneMin.length - 1]?.close ||
            0,
          stopLoss: validatedSignal?.stopLoss || 0,
          takeProfit: validatedSignal?.takeProfit || [],
          positionSize: this.calculatePositionSize(
            riskTolerance,
            validatedSignal?.confidence || 50
          ),
          leverage: validatedSignal?.leverage || 1,
          reasoning: validatedSignal?.reasoning || "基于技术分析的综合判断",
        },
        riskManagement: {
          maxDrawdown: parsedResponse.risk?.maxDrawdown || 5,
          riskReward: parsedResponse.risk?.riskReward || 2,
          winRate: parsedResponse.risk?.winRate || 60,
          expectedReturn: parsedResponse.risk?.expectedReturn || 0,
        },
        marketCondition: {
          volatility: this.classifyVolatility(
            this.calculateVolatility(marketData.daily)
          ),
          volume: this.classifyVolume(marketData.daily),
          momentum: parsedResponse.condition?.momentum || "NEUTRAL",
        },
        keyLevels: {
          support: parsedResponse.levels?.support || [indicators.support],
          resistance: parsedResponse.levels?.resistance || [
            indicators.resistance,
          ],
        },
        // 新增：庄家行为分析
        marketMakerAnalysis: {
          manipulationSignals: {
            washTrading:
              parsedResponse.marketMaker?.manipulationSignals?.washTrading || 0,
            priceSupport:
              parsedResponse.marketMaker?.manipulationSignals?.priceSupport ||
              0,
            volumeSpike:
              parsedResponse.marketMaker?.manipulationSignals?.volumeSpike || 0,
            falseBreakout:
              parsedResponse.marketMaker?.manipulationSignals?.falseBreakout ||
              0,
          },
          psychologyAnalysis: {
            fearGreedIndex:
              parsedResponse.marketMaker?.psychologyAnalysis?.fearGreedIndex ||
              50,
            marketSentiment:
              parsedResponse.marketMaker?.psychologyAnalysis?.marketSentiment ||
              "NEUTRAL",
            retailBehavior:
              parsedResponse.marketMaker?.psychologyAnalysis?.retailBehavior ||
              "RATIONAL",
            smartMoneyFlow:
              parsedResponse.marketMaker?.psychologyAnalysis?.smartMoneyFlow ||
              "NEUTRAL",
          },
          manipulationPatterns: {
            pattern:
              parsedResponse.marketMaker?.manipulationPatterns?.pattern ||
              "NONE",
            confidence:
              parsedResponse.marketMaker?.manipulationPatterns?.confidence || 0,
            stage:
              parsedResponse.marketMaker?.manipulationPatterns?.stage ||
              "EARLY",
            timeframe:
              parsedResponse.marketMaker?.manipulationPatterns?.timeframe ||
              "未知",
            description:
              parsedResponse.marketMaker?.manipulationPatterns?.description ||
              "暂无明显操盘模式",
          },
          intentions: {
            primaryGoal:
              parsedResponse.marketMaker?.intentions?.primaryGoal ||
              "MAINTAIN_RANGE",
            targetPrice:
              parsedResponse.marketMaker?.intentions?.targetPrice || 0,
            confidence: parsedResponse.marketMaker?.intentions?.confidence || 0,
            reasoning:
              parsedResponse.marketMaker?.intentions?.reasoning ||
              "数据不足，无法判断庄家意图",
          },
        },

        // 新增：现货期货分析
        spotFuturesAnalysis: {
          marketType: "FUTURES", // 默认合约
          basisSpread: parsedResponse.spotFutures?.basisSpread,
          fundingRate: parsedResponse.spotFutures?.fundingRate,
          openInterest: parsedResponse.spotFutures?.openInterest,
          leverageImpact: parsedResponse.spotFutures?.leverageImpact
            ? {
                averageLeverage:
                  parsedResponse.spotFutures.leverageImpact.averageLeverage ||
                  1,
                liquidationRisk:
                  parsedResponse.spotFutures.leverageImpact.liquidationRisk ||
                  "LOW",
                cascadeLiquidationPotential:
                  parsedResponse.spotFutures.leverageImpact
                    .cascadeLiquidationPotential || 0,
              }
            : undefined,
          arbitrageOpportunities: parsedResponse.spotFutures
            ?.arbitrageOpportunities
            ? {
                spotFuturesPremium:
                  parsedResponse.spotFutures.arbitrageOpportunities
                    .spotFuturesPremium || 0,
                crossExchangeSpread:
                  parsedResponse.spotFutures.arbitrageOpportunities
                    .crossExchangeSpread || 0,
                fundingArbitrage:
                  parsedResponse.spotFutures.arbitrageOpportunities
                    .fundingArbitrage || 0,
              }
            : undefined,
          recommendations: {
            preferredMarket:
              parsedResponse.spotFutures?.recommendations?.preferredMarket ||
              "FUTURES",
            reasoning:
              parsedResponse.spotFutures?.recommendations?.reasoning ||
              "基于当前市场条件的建议",
            riskAdjustments:
              parsedResponse.spotFutures?.recommendations?.riskAdjustments ||
              [],
          },
        },

        // 新增：多时间段预测分析
        multiTimeframePrediction: {
          symbol: marketData.symbol,
          timestamp: Date.now(),
          currentPrice:
            marketData.oneMin[marketData.oneMin.length - 1]?.close || 0,
          predictions: {
            ultraShort: {
              timeframe: "ULTRA_SHORT" as const,
              duration:
                parsedResponse.multiTimeframePrediction?.predictions?.ultraShort
                  ?.duration || "15分钟-2小时",
              startTime:
                parsedResponse.multiTimeframePrediction?.predictions?.ultraShort
                  ?.startTime || Date.now(), // 当前时间作为开始时间
              endTime:
                parsedResponse.multiTimeframePrediction?.predictions?.ultraShort
                  ?.endTime || Date.now() + 2 * 60 * 60 * 1000, // 2小时后
              trend:
                parsedResponse.multiTimeframePrediction?.predictions?.ultraShort
                  ?.trend || "NEUTRAL",
              confidence:
                parsedResponse.multiTimeframePrediction?.predictions?.ultraShort
                  ?.confidence || 50,
              targetPrice: {
                high:
                  parsedResponse.multiTimeframePrediction?.predictions
                    ?.ultraShort?.targetPrice?.high || 0,
                low:
                  parsedResponse.multiTimeframePrediction?.predictions
                    ?.ultraShort?.targetPrice?.low || 0,
                most_likely:
                  parsedResponse.multiTimeframePrediction?.predictions
                    ?.ultraShort?.targetPrice?.most_likely || 0,
              },
              keyEvents:
                parsedResponse.multiTimeframePrediction?.predictions?.ultraShort
                  ?.keyEvents || [],
              riskFactors:
                parsedResponse.multiTimeframePrediction?.predictions?.ultraShort
                  ?.riskFactors || [],
              opportunities:
                parsedResponse.multiTimeframePrediction?.predictions?.ultraShort
                  ?.opportunities || [],
              tradingStrategy: {
                action:
                  parsedResponse.multiTimeframePrediction?.predictions
                    ?.ultraShort?.tradingStrategy?.action || "HOLD",
                entryPrice:
                  parsedResponse.multiTimeframePrediction?.predictions
                    ?.ultraShort?.tradingStrategy?.entryPrice || 0,
                stopLoss:
                  parsedResponse.multiTimeframePrediction?.predictions
                    ?.ultraShort?.tradingStrategy?.stopLoss || 0,
                takeProfit:
                  parsedResponse.multiTimeframePrediction?.predictions
                    ?.ultraShort?.tradingStrategy?.takeProfit || [],
                positionSize: this.parsePositionSize(
                  parsedResponse.multiTimeframePrediction?.predictions
                    ?.ultraShort?.tradingStrategy?.positionSize || 2
                ),
                reasoning:
                  parsedResponse.multiTimeframePrediction?.predictions
                    ?.ultraShort?.tradingStrategy?.reasoning || "",
                riskLevel:
                  parsedResponse.multiTimeframePrediction?.predictions
                    ?.ultraShort?.tradingStrategy?.riskLevel || "HIGH",
                suitableFor:
                  parsedResponse.multiTimeframePrediction?.predictions
                    ?.ultraShort?.tradingStrategy?.suitableFor ||
                  "剥头皮交易，快进快出（主要交易建议）",
              },
              marketConditions: {
                volatility:
                  parsedResponse.multiTimeframePrediction?.predictions
                    ?.ultraShort?.marketConditions?.volatility || "MEDIUM",
                volume:
                  parsedResponse.multiTimeframePrediction?.predictions
                    ?.ultraShort?.marketConditions?.volume || "MEDIUM",
                momentum:
                  parsedResponse.multiTimeframePrediction?.predictions
                    ?.ultraShort?.marketConditions?.momentum || "NEUTRAL",
              },
            },
            short: {
              timeframe: "SHORT" as const,
              duration:
                parsedResponse.multiTimeframePrediction?.predictions?.short
                  ?.duration || "2-8小时",
              startTime:
                parsedResponse.multiTimeframePrediction?.predictions?.short
                  ?.startTime || Date.now(), // 当前时间作为开始时间
              endTime:
                parsedResponse.multiTimeframePrediction?.predictions?.short
                  ?.endTime || Date.now() + 8 * 60 * 60 * 1000, // 8小时后
              trend:
                parsedResponse.multiTimeframePrediction?.predictions?.short
                  ?.trend || "NEUTRAL",
              confidence:
                parsedResponse.multiTimeframePrediction?.predictions?.short
                  ?.confidence || 50,
              targetPrice: {
                high:
                  parsedResponse.multiTimeframePrediction?.predictions?.short
                    ?.targetPrice?.high || 0,
                low:
                  parsedResponse.multiTimeframePrediction?.predictions?.short
                    ?.targetPrice?.low || 0,
                most_likely:
                  parsedResponse.multiTimeframePrediction?.predictions?.short
                    ?.targetPrice?.most_likely || 0,
              },
              keyEvents:
                parsedResponse.multiTimeframePrediction?.predictions?.short
                  ?.keyEvents || [],
              riskFactors:
                parsedResponse.multiTimeframePrediction?.predictions?.short
                  ?.riskFactors || [],
              opportunities:
                parsedResponse.multiTimeframePrediction?.predictions?.short
                  ?.opportunities || [],
              tradingStrategy: {
                action:
                  parsedResponse.multiTimeframePrediction?.predictions?.short
                    ?.tradingStrategy?.action || "HOLD",
                entryPrice:
                  parsedResponse.multiTimeframePrediction?.predictions?.short
                    ?.tradingStrategy?.entryPrice || 0,
                stopLoss:
                  parsedResponse.multiTimeframePrediction?.predictions?.short
                    ?.tradingStrategy?.stopLoss || 0,
                takeProfit:
                  parsedResponse.multiTimeframePrediction?.predictions?.short
                    ?.tradingStrategy?.takeProfit || [],
                positionSize: this.parsePositionSize(
                  parsedResponse.multiTimeframePrediction?.predictions?.short
                    ?.tradingStrategy?.positionSize || 4
                ),
                reasoning:
                  parsedResponse.multiTimeframePrediction?.predictions?.short
                    ?.tradingStrategy?.reasoning || "",
                riskLevel:
                  parsedResponse.multiTimeframePrediction?.predictions?.short
                    ?.tradingStrategy?.riskLevel || "MEDIUM",
                suitableFor:
                  parsedResponse.multiTimeframePrediction?.predictions?.short
                    ?.tradingStrategy?.suitableFor || "日内波段交易",
              },
              marketConditions: {
                volatility:
                  parsedResponse.multiTimeframePrediction?.predictions?.short
                    ?.marketConditions?.volatility || "MEDIUM",
                volume:
                  parsedResponse.multiTimeframePrediction?.predictions?.short
                    ?.marketConditions?.volume || "MEDIUM",
                momentum:
                  parsedResponse.multiTimeframePrediction?.predictions?.short
                    ?.marketConditions?.momentum || "NEUTRAL",
              },
            },
            medium: {
              timeframe: "MEDIUM" as const,
              duration:
                parsedResponse.multiTimeframePrediction?.predictions?.medium
                  ?.duration || "8-24小时",
              startTime:
                parsedResponse.multiTimeframePrediction?.predictions?.medium
                  ?.startTime || Date.now(), // 当前时间作为开始时间
              endTime:
                parsedResponse.multiTimeframePrediction?.predictions?.medium
                  ?.endTime || Date.now() + 24 * 60 * 60 * 1000, // 24小时后
              trend:
                parsedResponse.multiTimeframePrediction?.predictions?.medium
                  ?.trend || "NEUTRAL",
              confidence:
                parsedResponse.multiTimeframePrediction?.predictions?.medium
                  ?.confidence || 50,
              targetPrice: {
                high:
                  parsedResponse.multiTimeframePrediction?.predictions?.medium
                    ?.targetPrice?.high || 0,
                low:
                  parsedResponse.multiTimeframePrediction?.predictions?.medium
                    ?.targetPrice?.low || 0,
                most_likely:
                  parsedResponse.multiTimeframePrediction?.predictions?.medium
                    ?.targetPrice?.most_likely || 0,
              },
              keyEvents:
                parsedResponse.multiTimeframePrediction?.predictions?.medium
                  ?.keyEvents || [],
              riskFactors:
                parsedResponse.multiTimeframePrediction?.predictions?.medium
                  ?.riskFactors || [],
              opportunities:
                parsedResponse.multiTimeframePrediction?.predictions?.medium
                  ?.opportunities || [],
              tradingStrategy: {
                action:
                  parsedResponse.multiTimeframePrediction?.predictions?.medium
                    ?.tradingStrategy?.action || "HOLD",
                entryPrice:
                  parsedResponse.multiTimeframePrediction?.predictions?.medium
                    ?.tradingStrategy?.entryPrice || 0,
                stopLoss:
                  parsedResponse.multiTimeframePrediction?.predictions?.medium
                    ?.tradingStrategy?.stopLoss || 0,
                takeProfit:
                  parsedResponse.multiTimeframePrediction?.predictions?.medium
                    ?.tradingStrategy?.takeProfit || [],
                positionSize: this.parsePositionSize(
                  parsedResponse.multiTimeframePrediction?.predictions?.medium
                    ?.tradingStrategy?.positionSize || 6
                ),
                reasoning:
                  parsedResponse.multiTimeframePrediction?.predictions?.medium
                    ?.tradingStrategy?.reasoning || "",
                riskLevel:
                  parsedResponse.multiTimeframePrediction?.predictions?.medium
                    ?.tradingStrategy?.riskLevel || "MEDIUM-LOW",
                suitableFor:
                  parsedResponse.multiTimeframePrediction?.predictions?.medium
                    ?.tradingStrategy?.suitableFor || "短线持仓交易",
              },
              marketConditions: {
                volatility:
                  parsedResponse.multiTimeframePrediction?.predictions?.medium
                    ?.marketConditions?.volatility || "MEDIUM",
                volume:
                  parsedResponse.multiTimeframePrediction?.predictions?.medium
                    ?.marketConditions?.volume || "MEDIUM",
                momentum:
                  parsedResponse.multiTimeframePrediction?.predictions?.medium
                    ?.marketConditions?.momentum || "NEUTRAL",
              },
            },
          },
          overallAssessment: {
            dominantTrend:
              parsedResponse.multiTimeframePrediction?.overallAssessment
                ?.dominantTrend || "NEUTRAL",
            conflictingSignals:
              parsedResponse.multiTimeframePrediction?.overallAssessment
                ?.conflictingSignals || false,
            riskLevel:
              parsedResponse.multiTimeframePrediction?.overallAssessment
                ?.riskLevel || "MEDIUM",
            recommendation:
              parsedResponse.multiTimeframePrediction?.overallAssessment
                ?.recommendation || "基于当前市场条件的综合建议",
            primaryTimeframe:
              parsedResponse.multiTimeframePrediction?.overallAssessment
                ?.primaryTimeframe || "ultraShort",
          },
          correlationAnalysis: {
            timeframeAlignment:
              parsedResponse.multiTimeframePrediction?.correlationAnalysis
                ?.timeframeAlignment || 50,
            trendStrength:
              parsedResponse.multiTimeframePrediction?.correlationAnalysis
                ?.trendStrength || 50,
            reversal_probability:
              parsedResponse.multiTimeframePrediction?.correlationAnalysis
                ?.reversal_probability || 50,
          },
        },

        aiInsights:
          parsedResponse.insights ||
          "基于当前市场数据的综合分析，包含主力行为分析和技术指标分析",
        warnings: parsedResponse.warnings || [],
      };

      // 记录预测用于后续验证（异步执行，不阻塞主流程）
      if (result.tradingSignal.direction !== "HOLD") {
        // 构建结构化的 reasoning 数据（符合项目标准格式）
        const reasoningData = {
          analysis: result.tradingSignal.reasoning || "AI生成的交易分析",
          indicators: [
            `RSI: ${indicators.rsi.toFixed(2)}`,
            `MACD: ${indicators.macd.macd.toFixed(4)}`,
            `EMA20: ${indicators.ema.ema20.toFixed(4)}`,
            `支撑位: ${indicators.support.toFixed(4)}`,
            `阻力位: ${indicators.resistance.toFixed(4)}`,
          ],
          // 添加范围验证配置（极短期交易的默认配置）
          priceRangeValidation: {
            enabled: true,
            samplingInterval: 5, // 5分钟采样
            tolerancePercent: 0.5, // 0.5%容忍度
          },
          metadata: {
            createdAt: new Date().toISOString(),
            tradingType: "ultraShort",
            configSource: "timeframe",
          },
        };

        serverPredictionTracker
          .recordPrediction({
            symbol: result.symbol,
            direction:
              result.tradingSignal.direction === "LONG" ? "BUY" : "SELL",
            entryPrice: result.tradingSignal.entryPrice,
            targetPrice:
              result.tradingSignal.takeProfit[0] ||
              result.tradingSignal.entryPrice * 1.05,
            stopLoss: result.tradingSignal.stopLoss,
            timeframe:
              result.multiTimeframePrediction.predictions.ultraShort.duration,
            confidence: result.tradingSignal.confidence,
            reasoning: JSON.stringify(reasoningData),
          })
          .then((predictionId) => {
            console.log(`预测已记录，ID: ${predictionId}`);
          })
          .catch((error) => {
            console.warn("记录预测失败:", error);
          });
      }

      return result;
    } catch (error) {
      console.error(
        "AI分析失败:",
        typeof error === "object" ? JSON.stringify(error, null, 2) : error
      );
      throw new Error("AI分析服务暂时不可用，请稍后重试");
    }
  }

  /**
   * 执行AI分析（使用实例配置，向后兼容）
   */
  async analyzeMarket(
    marketData: MarketData,
    indicators: TechnicalIndicators,
    riskTolerance: "LOW" | "MEDIUM" | "HIGH" = "MEDIUM"
  ): Promise<AIAnalysisResult> {
    if (!this.openai || !this.currentConfig) {
      throw new Error("AI 服务未配置，请前往设置页面配置 OpenAI API");
    }

    return this.analyzeMarketWithConfig(
      marketData,
      indicators,
      this.currentConfig,
      riskTolerance
    );
  }

  /**
   * 计算建议仓位大小（优化版 - 降低回撤）
   */
  private calculatePositionSize(
    riskTolerance: string,
    confidence: number
  ): number {
    const baseSize =
      {
        LOW: 2, // 降低保守型基础仓位
        MEDIUM: 3, // 降低平衡型基础仓位
        HIGH: 5, // 降低激进型基础仓位
      }[riskTolerance] || 3;

    // 根据信心度调整仓位（更保守的策略）
    const confidenceMultiplier = Math.max(0.5, confidence / 100); // 最低50%仓位
    const adjustedSize = baseSize * confidenceMultiplier;

    // 更严格的最大仓位限制
    const maxSize =
      {
        LOW: 4, // 保守型最大4%
        MEDIUM: 6, // 平衡型最大6%
        HIGH: 8, // 激进型最大8%
      }[riskTolerance] || 6;

    return Math.min(adjustedSize, maxSize);
  }

  /**
   * 解析仓位大小，处理字符串和数字类型
   */
  private parsePositionSize(positionSize: any): number {
    if (typeof positionSize === "number") {
      return positionSize;
    }

    if (typeof positionSize === "string") {
      // 移除百分号并转换为数字
      const numStr = positionSize.replace("%", "").trim();
      const num = parseFloat(numStr);
      return isNaN(num) ? 5 : num; // 默认5%
    }

    return 5; // 默认5%
  }

  /**
   * 分类波动率
   */
  private classifyVolatility(volatility: number): "HIGH" | "MEDIUM" | "LOW" {
    if (volatility > 80) return "HIGH";
    if (volatility > 40) return "MEDIUM";
    return "LOW";
  }

  /**
   * 分类成交量
   */
  private classifyVolume(dailyData: any[]): "HIGH" | "MEDIUM" | "LOW" {
    if (dailyData.length < 7) return "MEDIUM";

    const recent = dailyData[dailyData.length - 1]?.volume || 0;
    const average =
      dailyData.slice(-7).reduce((sum, d) => sum + d.volume, 0) / 7;

    const ratio = recent / average;
    if (ratio > 1.5) return "HIGH";
    if (ratio > 0.8) return "MEDIUM";
    return "LOW";
  }

  /**
   * 转换信号方向：将AI返回的BUY/SELL转换为LONG/SHORT
   */
  private convertSignalDirection(
    direction: string | undefined
  ): "LONG" | "SHORT" | "HOLD" {
    if (!direction) return "HOLD";

    switch (direction.toUpperCase()) {
      case "BUY":
        return "LONG";
      case "SELL":
        return "SHORT";
      case "LONG":
        return "LONG";
      case "SHORT":
        return "SHORT";
      case "HOLD":
        return "HOLD";
      default:
        return "HOLD";
    }
  }

  /**
   * 验证交易信号的逻辑一致性
   */
  private validateTradingSignal(signal: any): any {
    if (!signal) return signal;

    const { direction, entryPrice, stopLoss, takeProfit } = signal;

    // 如果是买入(LONG)信号
    if (direction === "BUY" || direction === "LONG") {
      // 止损应该低于入场价，止盈应该高于入场价
      if (stopLoss && stopLoss > entryPrice) {
        console.warn("买入信号的止损价格高于入场价格，自动修正");
        signal.stopLoss = entryPrice * 0.98; // 设置为入场价的98%
      }
      if (takeProfit && Array.isArray(takeProfit)) {
        signal.takeProfit = takeProfit.map(
          (tp) => (tp < entryPrice ? entryPrice * 1.05 : tp) // 确保止盈高于入场价
        );
      }
    }

    // 如果是卖出(SHORT)信号
    if (direction === "SELL" || direction === "SHORT") {
      // 止损应该高于入场价，止盈应该低于入场价
      if (stopLoss && stopLoss < entryPrice) {
        console.warn("卖出信号的止损价格低于入场价格，自动修正");
        signal.stopLoss = entryPrice * 1.02; // 设置为入场价的102%
      }
      if (takeProfit && Array.isArray(takeProfit)) {
        signal.takeProfit = takeProfit.map(
          (tp) => (tp > entryPrice ? entryPrice * 0.95 : tp) // 确保止盈低于入场价
        );
      }
    }

    return signal;
  }

  /**
   * 生成滚仓策略建议（优化版 - 降低回撤）
   */
  async generateRollingStrategy(
    _marketData: MarketData,
    analysisResult: AIAnalysisResult
  ): Promise<RollingStrategy> {
    const { tradingSignal, marketCondition } = analysisResult;

    // 基础仓位根据市场条件调整（更保守）
    let basePosition = 3; // 默认降低到3%
    if (marketCondition.volatility === "HIGH") basePosition = 2; // 高波动降低到2%
    if (marketCondition.volatility === "LOW") {
      // 低波动环境特殊处理
      if (analysisResult.marketTrend.shortTerm === "BEARISH") {
        basePosition = 2; // 低波动熊市更保守
      } else {
        basePosition = 3; // 低波动非熊市稍微积极
      }
    }

    // 加仓阈值根据趋势强度调整（更严格）
    let additionThreshold = tradingSignal.confidence > 85 ? 2.5 : 3.5;

    // 低波动熊市环境下提高加仓门槛
    if (
      marketCondition.volatility === "LOW" &&
      analysisResult.marketTrend.shortTerm === "BEARISH"
    ) {
      additionThreshold = 4.0; // 提高到4%才加仓
    }

    // 更严格的最大仓位控制
    const maxPositionMultiplier =
      marketCondition.volatility === "HIGH" ? 2 : 2.5;

    return {
      basePosition,
      additionThreshold,
      maxPosition: Math.min(basePosition * maxPositionMultiplier, 10), // 最大仓位不超过10%
      profitTarget:
        tradingSignal.takeProfit[0] || tradingSignal.entryPrice * 1.05,
      stopLoss: tradingSignal.stopLoss,
      trendConfirmation: tradingSignal.confidence > 75, // 提高确认要求
    };
  }
}

export const aiAnalysisService = AIAnalysisService.getInstance();
