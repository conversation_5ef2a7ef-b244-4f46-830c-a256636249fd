import axios from "axios";
import { formatPriceLocale, formatPercentage, divide } from "@/lib/big-utils";

/**
 * 飞书消息类型
 */
export type FeishuMessageType = "text" | "rich_text" | "interactive";

/**
 * 飞书富文本消息内容
 */
export interface FeishuRichTextContent {
  title?: string;
  content: Array<{
    tag: string;
    text?: string;
    style?: string[];
    href?: string;
    user_id?: string;
  }>;
}

/**
 * 飞书消息配置
 */
export interface FeishuMessageConfig {
  webhookUrl: string;
  msgType: FeishuMessageType;
  content: any;
  atAll?: boolean;
  atUsers?: string[];
}

/**
 * 交易建议消息格式 - 扩展版本，包含完整的交易建议信息
 */
export interface TradingAdviceMessage {
  symbol: string;
  action: "BUY" | "SELL" | "HOLD";
  entryPrice: number;
  quantity: number;
  stopLoss: number;
  takeProfit: number[];
  timeframe: string;
  confidence: number;
  reasoning: string;
  riskLevel: "LOW" | "MEDIUM" | "HIGH";
  timestamp: string;
  marketData: {
    latestPrice: number;
    dailyChange: number;
    volume24h: number;
  };
  // 新增字段
  positionManagement?: {
    initialPosition: number;
    addPositions: Array<{
      price: number;
      size: number;
      condition: string;
    }>;
    exitStrategy: {
      partialExits: Array<{
        price: number;
        percentage: number;
      }>;
      stopLoss: number;
      trailingStop: boolean;
    };
  };
  rollingStrategy?: {
    basePosition: number;
    additionThreshold: number;
    maxPosition: number;
    profitTarget: number;
    stopLoss: number;
    trendConfirmation: boolean;
  };
  riskReward?: number;
  multiTimeframePrediction?: {
    currentPrice: number;
    overallAssessment: {
      dominantTrend: "BULLISH" | "BEARISH" | "NEUTRAL";
      recommendation: string;
      primaryTimeframe: "ultraShort" | "short" | "medium";
    };
    predictions: {
      ultraShort: {
        duration: string;
        trend: "BULLISH" | "BEARISH" | "NEUTRAL";
        confidence: number;
        targetPrice: {
          high: number;
          low: number;
          most_likely: number;
        };
        tradingStrategy: {
          action: "BUY" | "SELL" | "HOLD";
          entryPrice: number;
          stopLoss: number;
          takeProfit: number[];
          positionSize: number;
          riskLevel: "HIGH" | "MEDIUM" | "MEDIUM-LOW";
          suitableFor: string;
        };
      };
      short: {
        duration: string;
        trend: "BULLISH" | "BEARISH" | "NEUTRAL";
        confidence: number;
        targetPrice: {
          high: number;
          low: number;
          most_likely: number;
        };
        tradingStrategy: {
          action: "BUY" | "SELL" | "HOLD";
          entryPrice: number;
          stopLoss: number;
          takeProfit: number[];
          positionSize: number;
          riskLevel: "HIGH" | "MEDIUM" | "MEDIUM-LOW";
          suitableFor: string;
        };
      };
      medium: {
        duration: string;
        trend: "BULLISH" | "BEARISH" | "NEUTRAL";
        confidence: number;
        targetPrice: {
          high: number;
          low: number;
          most_likely: number;
        };
        tradingStrategy: {
          action: "BUY" | "SELL" | "HOLD";
          entryPrice: number;
          stopLoss: number;
          takeProfit: number[];
          positionSize: number;
          riskLevel: "HIGH" | "MEDIUM" | "MEDIUM-LOW";
          suitableFor: string;
        };
      };
    };
    correlationAnalysis: {
      timeframeAlignment: number;
      trendStrength: number;
      reversal_probability: number;
    };
  };
}

/**
 * 预测验证报告消息格式
 */
export interface PredictionValidationReport {
  verifiedCount: number;
  totalPredictions: number;
  verifiedPredictions: number;
  directionAccuracy: number;
  priceAccuracy: number;
  overallAccuracy: number;
  bySymbol: Record<string, number>;
  byTimeframe: Record<string, number>;
  byConfidence: Record<string, number>;
  recentTrend: number;
  suggestions: string[];
  timestamp: string;
  // 新增详细统计信息
  detailedStats?: {
    hitTargetRate: number; // 达到目标价格的比例
    hitStopLossRate: number; // 触及止损的比例
    avgProfitLoss: number; // 平均盈亏比例
    bestPerformingStrategy: string; // 表现最好的策略
    worstPerformingStrategy: string; // 表现最差的策略
    marketConditionAnalysis: string; // 市场环境分析
    riskMetrics: {
      maxDrawdown: number; // 最大回撤
      sharpeRatio: number; // 夏普比率
      winRate: number; // 胜率
    };
    performanceTrends: {
      last7Days: number; // 最近7天准确率
      last30Days: number; // 最近30天准确率
      improvement: number; // 改进幅度
    };
  };
}

/**
 * 飞书Webhook服务类
 */
export class FeishuWebhookService {
  private webhookUrl: string;

  constructor(webhookUrl: string) {
    this.webhookUrl = webhookUrl;
  }

  /**
   * 发送文本消息
   */
  async sendTextMessage(
    text: string,
    atAll: boolean = false
  ): Promise<boolean> {
    try {
      const payload = {
        msg_type: "text",
        content: {
          text: atAll ? `${text} <at user_id="all">所有人</at>` : text,
        },
      };

      console.log("发送飞书文本消息，URL:", this.webhookUrl);

      const response = await axios.post(this.webhookUrl, payload, {
        headers: {
          "Content-Type": "application/json",
        },
        timeout: 10000,
      });

      console.log("飞书文本消息响应状态:", response.status);
      console.log(
        "飞书文本消息响应数据:",
        JSON.stringify(response.data, null, 2)
      );

      const success = response.status === 200 && response.data.code === 0;
      console.log("飞书文本消息发送结果:", success ? "成功" : "失败");

      return success;
    } catch (error: any) {
      console.error("发送飞书文本消息失败:", error);
      if (error.response) {
        console.error("飞书API响应错误:", {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data,
        });
      } else if (error.request) {
        console.error("飞书API网络错误:", error.request);
      } else {
        console.error("飞书API其他错误:", error.message);
      }
      return false;
    }
  }

  /**
   * 发送富文本消息
   */
  async sendRichTextMessage(
    title: string,
    content: FeishuRichTextContent["content"],
    atAll: boolean = false
  ): Promise<boolean> {
    try {
      const payload = {
        msg_type: "rich_text",
        content: {
          rich_text: {
            title: {
              tag: "text",
              text: title,
            },
            content: [
              {
                tag: "p",
                children: content,
              },
              ...(atAll
                ? [
                    {
                      tag: "p",
                      children: [
                        {
                          tag: "at",
                          user_id: "all",
                        },
                      ],
                    },
                  ]
                : []),
            ],
          },
        },
      };

      console.log("发送飞书富文本消息，URL:", this.webhookUrl);
      console.log(
        "发送飞书富文本消息，payload:",
        JSON.stringify(payload, null, 2)
      );

      const response = await axios.post(this.webhookUrl, payload, {
        headers: {
          "Content-Type": "application/json",
        },
        timeout: 10000,
      });

      console.log("飞书富文本消息响应状态:", response.status);
      console.log(
        "飞书富文本消息响应数据:",
        JSON.stringify(response.data, null, 2)
      );

      const success = response.status === 200 && response.data.code === 0;
      console.log("飞书富文本消息发送结果:", success ? "成功" : "失败");

      return success;
    } catch (error: any) {
      console.error("发送飞书富文本消息失败:", error);
      if (error.response) {
        console.error("飞书API响应错误:", {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data,
        });
      } else if (error.request) {
        console.error("飞书API网络错误:", error.request);
      } else {
        console.error("飞书API其他错误:", error.message);
      }
      return false;
    }
  }

  /**
   * 发送交易建议消息 - 完整版本
   */
  async sendTradingAdvice(
    advice: TradingAdviceMessage,
    atAll: boolean = false
  ): Promise<boolean> {
    try {
      const {
        symbol,
        action,
        entryPrice,
        quantity,
        stopLoss,
        takeProfit,
        timeframe,
        confidence,
        reasoning,
        riskLevel,
        timestamp,
        marketData,
        positionManagement,
        rollingStrategy,
        riskReward,
        multiTimeframePrediction,
      } = advice;

      // 根据操作类型设置颜色和图标
      const actionConfig = {
        BUY: { color: "green", icon: "🚀", text: "买入" },
        SELL: { color: "red", icon: "📉", text: "卖出" },
        HOLD: { color: "orange", icon: "⏸️", text: "观望" },
      };

      const config = actionConfig[action];

      // 构建完整的交易建议消息
      let message = `${config.icon} ${symbol} 激进型交易分析 - ${config.text}\n\n`;

      // 总体建议（提前显示）
      if (multiTimeframePrediction) {
        message += `💡 总体建议: ${multiTimeframePrediction.overallAssessment.recommendation}\n\n`;
      }

      // 策略说明（提前显示）
      message += `💡 策略说明:\n${reasoning}\n\n`;

      // 基本交易信息
      message += `📊 操作建议: ${config.text}\n`;
      message += `💰 入场价格: $${formatPriceLocale(entryPrice)}\n`;
      message += `🛡️ 止损价格: $${formatPriceLocale(stopLoss)}\n`;
      message += `🎯 止盈目标: ${takeProfit
        .map((tp) => `$${formatPriceLocale(tp)}`)
        .join(" / ")}\n`;
      message += `📈 建议数量: ${
        formatPercentage(quantity || 0, 4) || "N/A"
      }\n`;
      message += `⏰ 持有时间: ${timeframe || "N/A"}\n`;
      message += `🎲 信心度: ${formatPercentage(confidence, 1)}%\n`;
      message += `⚠️ 风险等级: ${riskLevel}\n`;
      if (riskReward) {
        message += `📊 风险收益比: 1:${formatPercentage(riskReward, 1)}\n`;
      }

      // 市场数据
      message += `\n📋 市场数据:\n`;
      message += `当前价格: $${formatPriceLocale(marketData.latestPrice)}\n`;
      message += `24h涨跌: ${
        marketData.dailyChange > 0 ? "+" : ""
      }${formatPercentage(marketData.dailyChange)}%\n`;
      message += `24h成交量: ${formatPercentage(
        divide(marketData.volume24h, 1000000),
        2
      )}M\n`;

      // 仓位管理策略
      if (positionManagement) {
        message += `\n� 仓位管理:\n`;
        message += `初始仓位: ${positionManagement.initialPosition}%\n`;
        if (positionManagement.addPositions.length > 0) {
          message += `加仓策略:\n`;
          positionManagement.addPositions.forEach((add, index) => {
            message += `  ${index + 1}. $${formatPriceLocale(add.price)} - ${
              add.size
            }% (${add.condition})\n`;
          });
        }
        if (positionManagement.exitStrategy.partialExits.length > 0) {
          message += `分批止盈:\n`;
          positionManagement.exitStrategy.partialExits.forEach(
            (exit, index) => {
              message += `  ${index + 1}. $${formatPriceLocale(exit.price)} - ${
                exit.percentage
              }%\n`;
            }
          );
        }
      }

      // 滚仓策略
      if (rollingStrategy) {
        message += `\n🔄 滚仓策略:\n`;
        message += `基础仓位: ${rollingStrategy.basePosition}%\n`;
        message += `加仓阈值: ${rollingStrategy.additionThreshold}%\n`;
        message += `最大仓位: ${rollingStrategy.maxPosition}%\n`;
        message += `盈利目标: ${rollingStrategy.profitTarget}%\n`;
        message += `止损位: ${rollingStrategy.stopLoss}%\n`;
        message += `趋势确认: ${
          rollingStrategy.trendConfirmation ? "是" : "否"
        }\n`;
      }

      // 多时间段预测（重点显示极短期，因为这是主要交易建议）
      if (multiTimeframePrediction) {
        message += `\n🔮 多时间段预测:\n`;
        message += `主导趋势: ${
          multiTimeframePrediction.overallAssessment.dominantTrend === "BULLISH"
            ? "看涨"
            : multiTimeframePrediction.overallAssessment.dominantTrend ===
              "BEARISH"
            ? "看跌"
            : "中性"
        }\n`;
        message += `时间段一致性: ${multiTimeframePrediction.correlationAnalysis.timeframeAlignment}%\n`;

        // 极短期预测（主要交易建议）
        const ultraShort = multiTimeframePrediction.predictions.ultraShort;
        message += `\n⚡ 极短期 (${ultraShort.duration}):\n`;
        message += `  趋势: ${
          ultraShort.trend === "BULLISH"
            ? "看涨"
            : ultraShort.trend === "BEARISH"
            ? "看跌"
            : "中性"
        } (${ultraShort.confidence}%)\n`;
        message += `  价格区间: $${formatPriceLocale(
          ultraShort.targetPrice.low
        )} - $${formatPriceLocale(ultraShort.targetPrice.high)}\n`;
        message += `  最可能价格: $${formatPriceLocale(
          ultraShort.targetPrice.most_likely
        )}\n`;
        message += `  建议操作: ${
          ultraShort.tradingStrategy.action === "BUY"
            ? "买入"
            : ultraShort.tradingStrategy.action === "SELL"
            ? "卖出"
            : "观望"
        }\n`;
        if (ultraShort.tradingStrategy.action !== "HOLD") {
          message += `  入场价: $${formatPriceLocale(
            ultraShort.tradingStrategy.entryPrice
          )}\n`;
          message += `  止损价: $${formatPriceLocale(
            ultraShort.tradingStrategy.stopLoss
          )}\n`;
          message += `  止盈价: $${formatPriceLocale(
            ultraShort.tradingStrategy.takeProfit[0] || 0
          )}\n`;
          message += `  建议仓位: ${ultraShort.tradingStrategy.positionSize}%\n`;
          message += `  风险等级: ${ultraShort.tradingStrategy.riskLevel}\n`;
          message += `  适合: ${ultraShort.tradingStrategy.suitableFor}\n`;
        }

        // 短期预测
        const short = multiTimeframePrediction.predictions.short;
        message += `\n🔥 短期 (${short.duration}):\n`;
        message += `  趋势: ${
          short.trend === "BULLISH"
            ? "看涨"
            : short.trend === "BEARISH"
            ? "看跌"
            : "中性"
        } (${short.confidence}%)\n`;
        message += `  价格区间: $${formatPriceLocale(
          short.targetPrice.low
        )} - $${formatPriceLocale(short.targetPrice.high)}\n`;
        message += `  最可能价格: $${formatPriceLocale(
          short.targetPrice.most_likely
        )}\n`;
        message += `  建议操作: ${
          short.tradingStrategy.action === "BUY"
            ? "买入"
            : short.tradingStrategy.action === "SELL"
            ? "卖出"
            : "观望"
        }\n`;
        if (short.tradingStrategy.action !== "HOLD") {
          message += `  入场价: $${formatPriceLocale(
            short.tradingStrategy.entryPrice
          )}\n`;
          message += `  止损价: $${formatPriceLocale(
            short.tradingStrategy.stopLoss
          )}\n`;
          message += `  止盈价: $${formatPriceLocale(
            short.tradingStrategy.takeProfit[0] || 0
          )}\n`;
          message += `  建议仓位: ${short.tradingStrategy.positionSize}%\n`;
          message += `  风险等级: ${short.tradingStrategy.riskLevel}\n`;
          message += `  适合: ${short.tradingStrategy.suitableFor}\n`;
        }

        // 中期预测
        const medium = multiTimeframePrediction.predictions.medium;
        message += `\n🎯 中期 (${medium.duration}):\n`;
        message += `  趋势: ${
          medium.trend === "BULLISH"
            ? "看涨"
            : medium.trend === "BEARISH"
            ? "看跌"
            : "中性"
        } (${medium.confidence}%)\n`;
        message += `  价格区间: $${formatPriceLocale(
          medium.targetPrice.low
        )} - $${formatPriceLocale(medium.targetPrice.high)}\n`;
        message += `  最可能价格: $${formatPriceLocale(
          medium.targetPrice.most_likely
        )}\n`;
        message += `  建议操作: ${
          medium.tradingStrategy.action === "BUY"
            ? "买入"
            : medium.tradingStrategy.action === "SELL"
            ? "卖出"
            : "观望"
        }\n`;
        if (medium.tradingStrategy.action !== "HOLD") {
          message += `  入场价: $${formatPriceLocale(
            medium.tradingStrategy.entryPrice
          )}\n`;
          message += `  止损价: $${formatPriceLocale(
            medium.tradingStrategy.stopLoss
          )}\n`;
          message += `  止盈价: $${formatPriceLocale(
            medium.tradingStrategy.takeProfit[0] || 0
          )}\n`;
          message += `  建议仓位: ${medium.tradingStrategy.positionSize}%\n`;
          message += `  风险等级: ${medium.tradingStrategy.riskLevel}\n`;
          message += `  适合: ${medium.tradingStrategy.suitableFor}\n`;
        }
      }

      // 风险提示
      message += `\n⚠️ 风险提示:\n`;
      message += `• 严格按照止损位执行\n`;
      message += `• 不要在震荡市中盲目加仓\n`;
      message += `• 保护已有利润，避免倒亏\n`;
      message += `• 根据市场变化及时调整策略\n`;

      message += `\n⏰ 分析时间: ${timestamp}`;

      return await this.sendTextMessage(message, atAll);
    } catch (error) {
      console.error("发送交易建议消息失败:", error);
      return false;
    }
  }

  /**
   * 发送错误消息
   */
  async sendErrorMessage(error: string, symbol?: string): Promise<boolean> {
    const message = symbol
      ? `❌ ${symbol} 分析失败: ${error}`
      : `❌ 系统错误: ${error}`;

    return await this.sendTextMessage(message);
  }

  /**
   * 发送系统状态消息
   */
  async sendStatusMessage(
    status: "started" | "stopped",
    interval: number
  ): Promise<boolean> {
    const statusText = status === "started" ? "启动" : "停止";
    const icon = status === "started" ? "✅" : "⏹️";

    const message = `${icon} 定时分析已${statusText}\n间隔: ${interval}分钟\n分析币种: BTC, ETH\n风险偏好: 激进型`;

    return await this.sendTextMessage(message, true);
  }

  /**
   * 发送预测验证报告
   */
  async sendValidationReport(
    report: PredictionValidationReport,
    atAll: boolean = false
  ): Promise<boolean> {
    try {
      let message = `📊 AI预测验证报告\n\n`;

      // 验证概况
      message += `🔍 本次验证: ${report.verifiedCount} 个预测\n`;
      message += `📈 总预测数: ${report.totalPredictions}\n`;
      message += `✅ 已验证数: ${report.verifiedPredictions}\n`;

      // 验证率
      const verificationRate =
        report.totalPredictions > 0
          ? (
              (report.verifiedPredictions / report.totalPredictions) *
              100
            ).toFixed(1)
          : "0.0";
      message += `📋 验证率: ${verificationRate}%\n\n`;

      // 核心准确率统计
      message += `📊 核心准确率统计:\n`;
      message += `• 方向准确率: ${report.directionAccuracy.toFixed(1)}%\n`;
      message += `• 价格准确率: ${report.priceAccuracy.toFixed(1)}%\n`;
      message += `• 综合准确率: ${report.overallAccuracy.toFixed(1)}%\n\n`;

      // 详细统计信息
      if (report.detailedStats) {
        const stats = report.detailedStats;

        message += `🎯 交易表现统计:\n`;
        message += `• 达标率: ${stats.hitTargetRate.toFixed(1)}%\n`;
        message += `• 止损率: ${stats.hitStopLossRate.toFixed(1)}%\n`;
        message += `• 胜率: ${stats.riskMetrics.winRate.toFixed(1)}%\n`;
        message += `• 平均盈亏: ${
          stats.avgProfitLoss > 0 ? "+" : ""
        }${stats.avgProfitLoss.toFixed(2)}%\n\n`;

        message += `📈 风险指标:\n`;
        message += `• 最大回撤: ${stats.riskMetrics.maxDrawdown.toFixed(2)}%\n`;
        message += `• 夏普比率: ${stats.riskMetrics.sharpeRatio.toFixed(
          2
        )}\n\n`;

        message += `📅 时间趋势:\n`;
        message += `• 最近7天: ${stats.performanceTrends.last7Days.toFixed(
          1
        )}%\n`;
        message += `• 最近30天: ${stats.performanceTrends.last30Days.toFixed(
          1
        )}%\n`;
        message += `• 改进幅度: ${
          stats.performanceTrends.improvement > 0 ? "+" : ""
        }${stats.performanceTrends.improvement.toFixed(1)}%\n\n`;
      }

      // 按币种统计
      if (Object.keys(report.bySymbol).length > 0) {
        message += `💰 按币种统计:\n`;
        Object.entries(report.bySymbol)
          .sort(([, a], [, b]) => b - a) // 按准确率降序排列
          .forEach(([symbol, accuracy]) => {
            const performanceIcon =
              accuracy >= 70 ? "🟢" : accuracy >= 50 ? "🟡" : "🔴";
            message += `${performanceIcon} ${symbol}: ${accuracy.toFixed(
              1
            )}%\n`;
          });
        message += `\n`;
      }

      // 按时间框架统计
      if (Object.keys(report.byTimeframe).length > 0) {
        message += `⏰ 按时间框架统计:\n`;
        Object.entries(report.byTimeframe)
          .sort(([, a], [, b]) => b - a) // 按准确率降序排列
          .forEach(([timeframe, accuracy]) => {
            const performanceIcon =
              accuracy >= 70 ? "🟢" : accuracy >= 50 ? "🟡" : "🔴";
            message += `${performanceIcon} ${timeframe}: ${accuracy.toFixed(
              1
            )}%\n`;
          });
        message += `\n`;
      }

      // 按信心度统计
      if (Object.keys(report.byConfidence).length > 0) {
        message += `🎯 按信心度统计:\n`;
        Object.entries(report.byConfidence)
          .sort(([, a], [, b]) => b - a) // 按准确率降序排列
          .forEach(([confidence, accuracy]) => {
            const performanceIcon =
              accuracy >= 70 ? "🟢" : accuracy >= 50 ? "🟡" : "🔴";
            message += `${performanceIcon} ${confidence}: ${accuracy.toFixed(
              1
            )}%\n`;
          });
        message += `\n`;
      }

      // 策略表现分析
      if (report.detailedStats) {
        message += `🏆 策略表现:\n`;
        message += `• 最佳策略: ${report.detailedStats.bestPerformingStrategy}\n`;
        message += `• 待改进策略: ${report.detailedStats.worstPerformingStrategy}\n`;
        message += `• 市场环境: ${report.detailedStats.marketConditionAnalysis}\n\n`;
      }

      // 趋势分析
      if (report.recentTrend !== 0) {
        const trendIcon = report.recentTrend > 0 ? "📈" : "📉";
        const trendText = report.recentTrend > 0 ? "上升" : "下降";
        const trendLevel =
          Math.abs(report.recentTrend) > 10
            ? "显著"
            : Math.abs(report.recentTrend) > 5
            ? "明显"
            : "轻微";
        message += `📈 准确率趋势: ${trendIcon} ${trendLevel}${trendText} ${Math.abs(
          report.recentTrend
        ).toFixed(1)}%\n\n`;
      }

      // 改进建议
      if (report.suggestions.length > 0) {
        message += `💡 AI优化建议:\n`;
        report.suggestions.forEach((suggestion, index) => {
          message += `${index + 1}. ${suggestion}\n`;
        });
        message += `\n`;
      }

      // 总结评级
      const overallGrade = this.getPerformanceGrade(report.overallAccuracy);
      message += `📝 综合评级: ${overallGrade}\n`;
      message += `⏰ 验证时间: ${report.timestamp}`;

      return await this.sendTextMessage(message, atAll);
    } catch (error) {
      console.error("发送预测验证报告失败:", error);
      return false;
    }
  }

  /**
   * 获取表现评级
   */
  private getPerformanceGrade(accuracy: number): string {
    if (accuracy >= 85) return "🏆 优秀 (A+)";
    if (accuracy >= 80) return "🥇 良好 (A)";
    if (accuracy >= 75) return "🥈 中上 (B+)";
    if (accuracy >= 70) return "🥉 中等 (B)";
    if (accuracy >= 60) return "⚠️ 及格 (C)";
    return "❌ 需改进 (D)";
  }

  /**
   * 测试连接
   */
  async testConnection(): Promise<boolean> {
    return await this.sendTextMessage("🔧 飞书机器人连接测试成功！");
  }
}

/**
 * 创建飞书Webhook服务实例
 */
export function createFeishuWebhookService(
  webhookUrl: string
): FeishuWebhookService {
  return new FeishuWebhookService(webhookUrl);
}
